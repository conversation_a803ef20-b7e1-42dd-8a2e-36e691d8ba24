import { McpServer } from '../pages/assistant/use-mcp-servers';

/**
 * 获取启用的工具状态
 * @param builtinTools 内置工具列表
 * @param mcpServers MCP 服务器列表
 * @returns 工具配置状态
 */
export function getEnabledToolsStatus(builtinTools: BuiltinTool[], mcpServers: McpServer[]) {
    // 这里应该根据本地存储的配置来确定哪些工具是启用的
    // 暂时返回一个基本的结构
    return {
        tools: builtinTools.map(tool => ({
            ...tool,
            enabled: true // 默认启用，实际应该从本地存储读取
        })),
        mcp: mcpServers.map(server => ({
            ...server,
            enabled: true // 默认启用，实际应该从本地存储读取
        }))
    };
}
