import { useCallback } from 'react';
import { useOptions } from '@topwrite/common';
import useLocalStorageStateWithBook from '../../lib/use-local-storage-state-with-book';
import useMcpServers, { McpServer } from './use-mcp-servers';

/**
 * 工具配置 Hook
 * 管理内置工具和 MCP 服务器工具的启用/禁用状态
 */
export default function useToolsConfig() {
    const { assistant } = useOptions();
    const { servers: mcpServers, addServer, deleteServer } = useMcpServers();
    const [builtinDisabledTools, setBuiltinDisabledTools] = useLocalStorageStateWithBook<string[]>('builtin-disabled-tools', []);
    
    // 获取内置工具列表
    const builtinTools = assistant.tools || [];

    // 切换单个内置工具的启用状态
    const toggleBuiltinTool = useCallback((toolName: string) => {
        setBuiltinDisabledTools(prev => {
            if (prev.includes(toolName)) {
                return prev.filter(name => name !== toolName);
            } else {
                return [...prev, toolName];
            }
        });
    }, [setBuiltinDisabledTools]);

    // 切换所有内置工具的启用状态
    const toggleAllBuiltinTools = useCallback((enabled: boolean) => {
        if (enabled) {
            setBuiltinDisabledTools([]);
        } else {
            setBuiltinDisabledTools(builtinTools.map(tool => tool.name));
        }
    }, [builtinTools, setBuiltinDisabledTools]);

    // 加载 MCP 服务器工具（这里是占位符，实际需要从服务器获取）
    const loadMcpServerTools = useCallback(async (serverName: string) => {
        // TODO: 实现从 MCP 服务器加载工具列表的逻辑
        console.log('Loading tools for server:', serverName);
    }, []);

    // 切换 MCP 工具的启用状态
    const toggleMcpTool = useCallback((serverName: string, toolName: string) => {
        // TODO: 实现 MCP 工具的切换逻辑
        console.log('Toggle MCP tool:', serverName, toolName);
    }, []);

    // 切换 MCP 服务器所有工具的启用状态
    const toggleMcpServerTools = useCallback((serverName: string, enabled: boolean) => {
        // TODO: 实现 MCP 服务器所有工具的切换逻辑
        console.log('Toggle all MCP server tools:', serverName, enabled);
    }, []);

    // 初始化 MCP 服务器
    const initializeMcpServers = useCallback(async (authorized: string | boolean) => {
        // TODO: 实现 MCP 服务器初始化逻辑
        console.log('Initialize MCP servers with auth:', authorized);
    }, []);

    // 添加 MCP 服务器
    const addMcpServer = useCallback(async (server: McpServer) => {
        await addServer(server);
    }, [addServer]);

    // 删除 MCP 服务器
    const deleteMcpServer = useCallback(async (serverName: string) => {
        await deleteServer(serverName);
    }, [deleteServer]);

    return {
        builtinTools,
        mcpServers,
        toggleBuiltinTool,
        toggleAllBuiltinTools,
        loadMcpServerTools,
        toggleMcpTool,
        toggleMcpServerTools,
        initializeMcpServers,
        addMcpServer,
        deleteMcpServer
    };
}
